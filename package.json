{"name": "salmate-frontend", "version": "0.0.1", "private": true, "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "serve": "node build", "serve:win": "node --env-file=.env build", "test": "npm run test:integration && npm run test:unit", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "lint": "prettier --check . && eslint .", "format": "prettier --write .", "test:integration": "playwright test", "test:unit": "vitest", "test:chat-center": "playwright test --project=chat-center-chromium", "test:ticket": "playwright test --project=ticket-chromium", "test:settings-account": "playwright test --project=settings-account-chromium", "test:settings-team": "playwright test --project=settings-team-chromium", "test:settings-general": "playwright test --project=settings-general-chromium", "test:auth": "playwright test --project=auth-chromium", "test:login": "playwright test --project=login-chromium"}, "devDependencies": {"@playwright/test": "^1.53.2", "@sveltejs/adapter-auto": "^3.0.0", "@sveltejs/adapter-node": "^5.2.12", "@sveltejs/kit": "^2.0.0", "@sveltejs/vite-plugin-svelte": "^3.0.0", "@tailwindcss/typography": "^0.5.14", "@types/eslint": "^9.6.0", "@types/node": "^24.0.10", "autoprefixer": "^10.4.20", "eslint": "^9.0.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-svelte": "^2.36.0", "flowbite-svelte-icons": "^1.6.1", "globals": "^15.0.0", "prettier": "^3.1.1", "prettier-plugin-svelte": "^3.1.2", "prettier-plugin-tailwindcss": "^0.6.5", "svelte": "^4.2.7", "svelte-check": "^3.6.0", "tailwindcss": "^3.3.2", "typescript": "^5.0.0", "typescript-eslint": "^8.0.0", "vite": "^5.0.3", "vite-plugin-remove-console": "^2.2.0", "vitest": "^2.0.0"}, "type": "module", "dependencies": {"@fortawesome/free-brands-svg-icons": "^6.6.0", "@fortawesome/free-solid-svg-icons": "^6.6.0", "@line/liff": "^2.27.0", "@rollup/plugin-typescript": "^12.1.4", "@tiptap/core": "^3.5.3", "@tiptap/extension-bold": "^3.5.3", "@tiptap/extension-code": "^3.6.1", "@tiptap/extension-color": "^3.5.3", "@tiptap/extension-highlight": "^3.5.3", "@tiptap/extension-subscript": "^3.6.1", "@tiptap/extension-superscript": "^3.6.1", "@tiptap/extension-table": "^3.5.3", "@tiptap/extension-table-cell": "^3.5.3", "@tiptap/extension-table-header": "^3.5.3", "@tiptap/extension-table-row": "^3.5.3", "@tiptap/extension-text-align": "^3.5.3", "@tiptap/extension-text-style": "^3.5.3", "@tiptap/extension-underline": "^3.5.3", "@tiptap/pm": "^3.5.3", "@tiptap/starter-kit": "^3.5.3", "bits-ui": "^0.21.13", "chart.js": "^4.5.0", "chartjs-plugin-datalabels": "^2.2.0", "chartjs-plugin-zoom": "^2.2.0", "clsx": "^2.1.1", "d3": "^7.9.0", "date-fns": "^4.1.0", "date-picker-svelte": "^2.15.1", "flatpickr": "^4.6.13", "flowbite": "^2.5.1", "flowbite-svelte": "^0.48.6", "formsnap": "^1.0.1", "i18n-iso-countries": "^7.14.0", "playwright": "^1.54.1", "svelte-fa": "^4.0.2", "svelte-flatpickr": "^3.3.5", "svelte-i18n": "^4.0.1", "sveltekit-superforms": "^2.17.0", "svelty-picker": "^5.2.12", "tailwind-merge": "^2.5.2", "tailwind-variants": "^0.2.1", "timeago.js": "^4.0.2", "world-countries": "^5.1.0", "zod": "^3.23.8"}}